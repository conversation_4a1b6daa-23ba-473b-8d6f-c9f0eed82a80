<?php


declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use app\api\model\EditLog;
use think\facade\Db;
use think\facade\Session;

class Test extends BaseController
{
	public function index()
	{

		// 获取订单详情
		$action_id = 22;
		$orderDetails = Db::name('customer_order_detail')->where(['order_id' => $action_id])->select();

        
		foreach ($orderDetails as $orderDetail) {
			$productId = $orderDetail['product_id'];
			$quantity = $orderDetail['quantity'];
			$orderDetail_id=$orderDetail['id'];
		
			// 检查产品是否有BOM表
			$bomExists = Db::name('material_bom')->where(['product_id' => $productId, 'status' => 1, 'delete_time' => 0])->find();

			if ($bomExists) {
				// 产品有BOM表，查询所有BOM物料
				$bomDetails = Db::name('material_bom_detail')->where(['bom_id' => $bomExists['id'], 'delete_time' => 0])->select();
				
				foreach ($bomDetails as $bomDetail) {
					$materialId = $bomDetail['material_id'];
					$requiredQuantity = $bomDetail['quantity'] * $quantity; // BOM用量 * 销售数量
					
					// 获取当前库存
					$currentStock = get_inventory_stock($materialId);
                    
					// 获取在途数量
					$inTransit = Db::name('purchase_order_detail')
						->alias('pod')
						->join('purchase_order po', 'po.id = pod.order_id')
						->where([
							'pod.product_id' => $materialId,
							['po.status', 'in', [1, 2]]
						])
						->sum('pod.quantity');
                    
                       
					// 计算可用库存
					$availableStock = $currentStock + $inTransit;
					
					// 如果库存不足，插入需求数据
					if ($availableStock < $requiredQuantity) {
						$needQuantity = $requiredQuantity - $availableStock;
						// 向物料需求表插入数据
						Db::name('material_requirement')->insert([
							'order_id' => $action_id,
							'product_id' => $materialId,
							'quantity' => $needQuantity,
							'source_id' => $orderDetail_id,
							'source_type' => 'customer_order',
							'status' => 0, // 未处理
							'gap'=>0,
							'create_time' => time(),
							'create_by' => $this->uid
						]);
					}
				}
			} else {
				// 产品没有BOM表，直接检查产品库存
				$inventory = Db::name('inventory')->where(['product_id' => $productId])->find();
				$currentStock = $inventory ? $inventory['quantity'] : 0;
				
				// 获取在途数量
				$inTransit = Db::name('purchase_order_detail')
					->alias('pod')
					->join('purchase_order po', 'po.id = pod.order_id')
					->where([
						'pod.product_id' => $productId,
						['po.status', 'in', [1, 2]] // 采购中或已审核状态
					])
					->sum('pod.quantity');
				
				// 计算可用库存
				$availableStock = $currentStock + $inTransit;
				
				// 如果库存不足，插入需求数据
				if ($availableStock < $quantity) {
					$needQuantity = $quantity - $availableStock;
					// 向物料需求表插入数据
					Db::name('material_requirement')->insert([
						'order_id' => $action_id,
						'product_id' => $productId,
						'quantity' => $needQuantity,
						'source_id' => $orderDetail_id,
						'source_type' => 'customer_order',
						'status' => 0, // 未处理
						'gap'=>0,
						'create_time' => time(),
						'create_by' => $this->uid
					]);
				}
			}
		}
		//end


		return 'hello';
	}
	//销售订单及物料需求表
	public function getmrppurchase_suggestion()
	{
		// 获取分页参数
		$page = intval(request()->param('page', 1));
		$limit = intval(request()->param('limit', 15));

		// 获取搜索参数
		$business_no = request()->param('business_no', '');
		$product_name = request()->param('product_name', '');
		$salesman_name = request()->param('salesman_name', '');

		// 1. 获取销售订单缺货数据
		$salesQuery = Db::name('customer_order_detail')
			->alias('cod')
			->join('product p', 'p.id = cod.product_id')
			->leftJoin('customer_order co', 'co.id = cod.order_id')
			->leftJoin('admin au', 'au.id = co.create_user_id')
			->where('co.check_status', 2)  // 已审核订单
			->where('cod.inventory_status', 2)  // 缺货状态
			->where('cod.gap', '>', 0);  // 有缺口

		// 2. 获取生产订单物料缺货数据
		$productionQuery = Db::name('produce_order_material_requirement')
			->alias('pomr')
			->join('product p', 'p.id = pomr.material_id')
			->leftJoin('produce_order po', 'po.id = pomr.order_id')
			->leftJoin('admin au', 'au.id = po.create_uid')
			->where('po.status', 'in', [1, 2, 3])  // 已排产、生产中、已完成
			->where('pomr.status', 'in', [1, 2])  // 待分配或部分分配
			->where('pomr.shortage_quantity', '>', 0);  // 有缺口的物料

		// 为销售订单查询添加搜索条件
		if (!empty($business_no)) {
			$salesQuery->where('co.order_no', 'like', "%{$business_no}%");
		}
		if (!empty($product_name)) {
			$salesQuery->where('p.title', 'like', "%{$product_name}%");
		}
		if (!empty($salesman_name)) {
			$salesQuery->where('au.nickname', 'like', "%{$salesman_name}%");
		}

		// 为生产订单查询添加搜索条件
		if (!empty($business_no)) {
			$productionQuery->where('po.order_no', 'like', "%{$business_no}%");
		}
		if (!empty($product_name)) {
			$productionQuery->where('pomr.material_name', 'like', "%{$product_name}%");
		}
		if (!empty($salesman_name)) {
			$productionQuery->where('au.nickname', 'like', "%{$salesman_name}%");
		}

		// 销售订单缺货数据字段
		$salesData = $salesQuery->field([
			'cod.id',
			'cod.product_id',
			'p.title as product_name',
			'p.material_code as product_code',
			'co.order_no as business_no',
			'cod.gap as gap',
			'cod.quantity as quantity',
			'au.nickname as salesman_name',
			'cod.create_time',
			'"sales_shortage" as source_type',
			'"销售缺货" as types',
			'co.id as source_order_id',
			'p.title as main_product_name',
			'cod.product_id as main_product_id'
		])->select()->toArray();

		// 生产订单物料缺货数据字段
		$productionData = $productionQuery->field([
			'pomr.id',
			'pomr.material_id as product_id',
			'pomr.material_name as product_name',
			'pomr.material_code as product_code',
			'po.order_no as business_no',
			'pomr.shortage_quantity as gap',
			'pomr.required_quantity as quantity',
			'au.nickname as salesman_name',
			'pomr.create_time',
			'"production_shortage" as source_type',
			'"生产缺料" as types',
			'po.id as source_order_id',
			'po.product_name as main_product_name',
			'po.product_id as main_product_id'
		])->select()->toArray();

		// 合并两种数据
		$allData = array_merge($salesData, $productionData);

		// 按创建时间倒序排序
		usort($allData, function($a, $b) {
			return $b['create_time'] - $a['create_time'];
		});

		// 计算总数
		$count = count($allData);

		// 手动分页
		$offset = ($page - 1) * $limit;
		$list = array_slice($allData, $offset, $limit);

		// 添加调试信息
		trace('销售缺货数据数量：' . count($salesData), 'debug');
		trace('生产缺料数据数量：' . count($productionData), 'debug');
		trace('合并后总数量：' . $count, 'debug');
		trace('分页参数：page=' . $page . ', limit=' . $limit, 'debug');

		if (!empty($list)) {
			trace('第一条记录信息：' . json_encode($list[0], JSON_UNESCAPED_UNICODE), 'debug');
		}

		// 获取所有产品ID，用于批量查询库存和在途数据
		$productIds = array_unique(array_column($list, 'product_id'));

		// 批量获取实时库存数据（使用新的inventory_realtime表）
		$inventoryMap = [];
		if (!empty($productIds)) {
			$inventoryData = Db::name('inventory_realtime')
				->field('product_id, SUM(quantity) as total_quantity, SUM(available_quantity) as available_quantity, SUM(locked_quantity) as locked_quantity')
				->whereIn('product_id', $productIds)
				->group('product_id')
				->select()
				->toArray();

			foreach ($inventoryData as $inventory) {
				$inventoryMap[$inventory['product_id']] = [
					'total_quantity' => $inventory['total_quantity'],
					'available_quantity' => $inventory['available_quantity'],
					'locked_quantity' => $inventory['locked_quantity']
				];
			}
		}

		// 批量获取在途库存数据（采购订单中未完全入库的数量）
		$inTransitMap = [];
		if (!empty($productIds)) {
			$inTransitData = Db::name('purchase_order_detail')
				->alias('d')
				->join('purchase_order o', 'd.order_id = o.id')
				->whereIn('d.product_id', $productIds)
				->where('o.status', 'in', [2, 3, 4]) // 已审核，部分入库，未完成的订单
				->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
				->field('d.product_id, SUM(d.quantity - d.received_quantity) as in_transit_quantity')
				->group('d.product_id')
				->select()
				->toArray();

			foreach ($inTransitData as $inTransit) {
				$inTransitMap[$inTransit['product_id']] = $inTransit['in_transit_quantity'];
			}
		}

		// 批量获取在途库存的锁定数据（采购订单相关的锁定）
		$inTransitLockedMap = [];
		if (!empty($productIds)) {
			$inTransitLockedData = Db::name('inventory_lock')
				->whereIn('product_id', $productIds)
				->where('ref_type', 'purchase_order') // 采购订单相关的锁定
				->where('status', 1) // 锁定中
				->field('product_id, SUM(quantity) as locked_quantity')
				->group('product_id')
				->select()
				->toArray();

			foreach ($inTransitLockedData as $locked) {
				$inTransitLockedMap[$locked['product_id']] = $locked['locked_quantity'];
			}
		}

		foreach ($list as &$item) {
			$item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);

			// 根据来源类型设置状态文本
			if ($item['source_type'] == 'sales_shortage') {
				$item['status_text'] = '销售缺货';
			} else {
				$item['status_text'] = '生产缺料';
			}

			// 添加库存信息
			$productId = $item['product_id'];

			// 在库数量（总库存）
			$item['stock_quantity'] = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['total_quantity'] : 0;

			// 在库可用数量（已扣除锁定）
			$stockAvailable = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['available_quantity'] : 0;

			// 在途总数量
			$item['in_transit_quantity'] = isset($inTransitMap[$productId]) ? $inTransitMap[$productId] : 0;

			// 在途锁定数量
			$inTransitLocked = isset($inTransitLockedMap[$productId]) ? $inTransitLockedMap[$productId] : 0;

			// 在途可用数量（在途总数量 - 在途锁定数量）
			$inTransitAvailable = max(0, $item['in_transit_quantity'] - $inTransitLocked);

			// 添加详细的库存信息到返回数据
			$item['stock_available'] = $stockAvailable; // 在库可用
			$item['in_transit_available'] = $inTransitAvailable; // 在途可用
			$item['in_transit_locked'] = $inTransitLocked; // 在途锁定
			$item['stock_locked'] = isset($inventoryMap[$productId]) ? $inventoryMap[$productId]['locked_quantity'] : 0; // 在库锁定

			// 重新计算剩余缺口（需求数量 - 在库可用 - 在途可用）
			$totalAvailable = $stockAvailable + $inTransitAvailable;
			$item['remaining_gap'] = max(0, $item['quantity'] - $totalAvailable);

			// 设置主/BOM标识
			if ($item['source_type'] == 'sales_shortage') {
				// 销售订单缺货
				if ($item['product_id'] == $item['main_product_id']) {
					$item['main_or_bom'] = '主产品';
					$item['item_type'] = '主产品';
				} else {
					$item['main_or_bom'] = '销售产品';
					$item['item_type'] = '销售产品';
				}
			} else {
				// 生产订单物料缺货
				$item['main_or_bom'] = '生产物料 (' . $item['main_product_name'] . ')';
				$item['item_type'] = '生产物料';
			}

			// 确保业务单号不为空
			if (empty($item['business_no'])) {
				$item['business_no'] = '未知订单';
			}
		}
		unset($item);

		// 包含分页信息的完整返回
		return json([
			'code' => 0,
			'msg' => 'success',
			'count' => $count,
			'data' => $list
		]);

	}


	  /**
     * 获取BOM详细信息
     */
    public function getBomInfo()
    {
        $product_id = request()->param('id/d', 0);
        $order_quantity = request()->param('qty/f', 1); // 订单数量，默认为1

        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询最新有效的BOM信息
        $bomInfo = Db::name('material_bom')
            ->where('product_id', $product_id)
            ->where('status', 1) // 启用状态
            ->where('delete_time', 0)
            ->order('create_time desc')
            ->find();

            
            
        if (!$bomInfo) {
            return json(['code' => 1, 'msg' => '该产品没有有效的BOM']);
        }
        
        // 获取产品信息
        $productInfo = Db::name('product')
            ->where('id', $product_id)
            ->field('id, title, material_code, specs, unit')
            ->find();
            
        // 获取BOM子项
        $bomItems = Db::name('material_bom_detail')
            ->alias('bd')
            ->join('product p', 'p.id = bd.material_id', 'left')
            ->where('bd.bom_id', $bomInfo['id'])
            ->where('bd.delete_time', 0) // 未删除
            ->field('bd.*, p.title as material_name, p.material_code, p.specs as product_specs, p.unit as uom_name')
            ->select()
            ->toArray();
            
        // 计算每个物料的需求和库存信息
        foreach ($bomItems as &$item) {
            // BOM单位用量
            $item['unit_quantity'] = floatval($item['quantity']);

            // 订单用量 = BOM单位用量 × 订单数量
            $item['order_quantity'] = $item['unit_quantity'] * $order_quantity;

            // 获取库存数量
            $item['stock'] = $this->getProductStock($item['material_id']);

            // 缺口数量 = 订单用量 - 库存数量（如果为负数则为0）
            $item['shortage'] = max(0, $item['order_quantity'] - $item['stock']);

            // 格式化数据，确保数值正确显示
            $item['unit_quantity'] = number_format($item['unit_quantity'], 4);
            $item['order_quantity'] = number_format($item['order_quantity'], 2);
            $item['stock'] = number_format($item['stock'], 2);
            $item['shortage'] = number_format($item['shortage'], 2);
        }
        
        $bomInfo['items'] = $bomItems;
        $bomInfo['product'] = $productInfo;
        $bomInfo['order_quantity'] = $order_quantity;

        return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomInfo]);
    }

	
    /**
     * 获取产品当前库存
     * @param int $product_id 产品ID
     * @return float 库存数量
     */
    private function getProductStock($product_id)
    {
        // 使用统一的库存获取函数
        return get_inventory_stock($product_id);
    }

}
