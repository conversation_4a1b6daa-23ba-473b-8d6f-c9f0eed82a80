# 销售订单自审核功能设计文档

## 功能概述

为销售订单列表页面增加简单的自审核功能，允许用户直接审核自己创建的订单，无需复杂的流程审核。

## 功能特点

1. **自审核机制**：用户只能审核自己创建的订单
2. **库存检查**：审核时自动检查库存并生成物料需求
3. **BOM展开**：支持有BOM的产品自动展开物料需求
4. **操作简单**：一键审核，无需复杂流程

## 实现细节

### 1. 控制器方法

#### `simpleCheck()` - 简单审核方法

**位置**：`app/customer/controller/Order.php`

**功能**：
- 检查权限（只能审核自己创建的订单）
- 检查订单状态（只能审核未审核的订单）
- 更新订单状态为已审核
- 自动进行库存检查和需求分析
- 记录操作日志

**权限控制**：
```php
// 检查权限 - 只能审核自己创建的订单
if($order->admin_id != $this->uid){
    return json(['code' => 1, 'msg' => '只能审核自己创建的订单', 'data' => null]);
}
```

**状态更新**：
```php
$update = [
    'status' => 1,           // 订单状态：处理中
    'check_status' => 2,     // 审核状态：已通过
    'check_time' => time(),  // 审核时间
    'check_user_id' => $this->uid,  // 审核人
    'update_time' => time()
];
```

### 2. 库存检查逻辑

#### BOM产品处理
1. 检查产品是否有BOM：`has_product_bom($product_id)`
2. 获取BOM信息：`get_product_bom($product_id)`
3. 获取BOM明细：`get_bom_items($bom_id)`
4. 计算物料需求：`$requiredQuantity = $bomItem['quantity'] * $detail['quantity']`
5. 检查库存：`get_inventory_stock($material_id)`
6. 生成需求记录

#### 普通产品处理
1. 直接检查产品库存
2. 计算缺口数量
3. 生成需求记录

#### 需求记录生成
```php
Db::name('material_requirement')->insert([
    'order_id' => $id,
    'product_id' => $product_id,
    'quantity' => $needQuantity,
    'source_id' => $detail['id'],
    'source_type' => 'customer_order_detail',
    'status' => 0, // 未处理
    'gap' => 0,
    'create_time' => time(),
    'create_by' => $this->uid
]);
```

### 3. 前端界面

#### 审核按钮显示条件
```html
{{#  if(d.check_status == 0){ }}
<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="check">审核</a>
{{#  } }}
```

#### 审核确认对话框
```javascript
layer.confirm('确定要审核该订单吗？<br><span style="color:red;">审核后将自动检查库存并生成物料需求</span>', {
    icon: 3, 
    title:'审核确认',
    area: ['400px', '200px']
}, function(index){
    // 执行审核操作
    tool.post("/customer/order/simpleCheck", {id: data.id}, callback);
});
```

## 业务流程

### 审核前状态
- 订单状态：`status = 0`（草稿）
- 审核状态：`check_status = 0`（未审核）

### 审核操作
1. 用户点击"审核"按钮
2. 系统检查权限和状态
3. 更新订单状态
4. 执行库存检查
5. 生成物料需求记录
6. 记录操作日志

### 审核后状态
- 订单状态：`status = 1`（处理中）
- 审核状态：`check_status = 2`（已通过）
- 审核时间：`check_time`
- 审核人：`check_user_id`

## 库存状态更新功能

### 新增功能需求
在 `simpleCheck` 审核过程中，需要更新 `customer_order_detail` 表中每个物料的：
- `inventory_status` 字段：库存状态（0未处理，1库存，2缺货，3补货完成）
- `gap` 字段：缺口数量

### 库存状态判断逻辑
1. **库存充足**：`inventory_status = 1`，`gap = 0`
2. **库存不足**：`inventory_status = 2`，`gap = 需求数量 - 可用库存`
3. **无库存**：`inventory_status = 2`，`gap = 需求数量`

### 实现方案
在审核时对每个订单明细进行库存检查：
1. 获取产品的可用库存数量
2. 比较需求数量与可用库存
3. 更新对应的库存状态和缺口数量
4. 支持BOM展开的产品库存检查

### 具体实现代码
```php
// 对每个订单明细更新库存状态
foreach ($orderDetails as $detail) {
    $productId = $detail['product_id'];
    $requiredQty = $detail['quantity'];
    $warehouseId = $detail['warehouse_id'] ?? 1;

    // 获取产品可用库存
    $inventoryStatus = $inventoryService->getProductAvailableQuantity($productId, $warehouseId);
    $availableQty = $inventoryStatus['available_quantity'];

    // 判断库存状态和缺口数量
    if ($availableQty >= $requiredQty) {
        // 库存充足
        $inventoryStatusCode = 1; // 库存
        $gap = 0;
    } else {
        // 库存不足或无库存
        $inventoryStatusCode = 2; // 缺货
        $gap = $requiredQty - $availableQty;
        if ($gap < 0) $gap = 0;
    }

    // 更新订单明细的库存状态
    Db::name('customer_order_detail')->where('id', $detail['id'])->update([
        'inventory_status' => $inventoryStatusCode,
        'gap' => $gap,
        'update_time' => time()
    ]);
}
```

### 库存不足处理策略

#### 当前处理方式
1. **允许审核通过**：即使库存不足也允许订单审核通过
2. **生成需求记录**：将库存缺口记录到`material_requirement`表
3. **后续补货**：通过需求管理模块处理补货
4. **状态记录**：在订单明细中记录具体的库存状态和缺口

#### 优点
- 不会因库存不足而阻止订单流程
- 自动生成补货需求
- 支持预售模式
- 提供详细的库存状态跟踪

#### 注意事项
- 需要完善的补货流程配合
- 发货时会再次检查库存
- 可能出现客户延期交货的情况
- 需要定期更新库存状态

## 相关文件

### 控制器文件
- `app/customer/controller/Order.php` - 添加`simpleCheck()`方法

### 视图文件
- `app/customer/view/order/index.html` - 添加审核按钮和事件处理

### 兼容函数
- `app/common.php` - BOM兼容函数
  - `has_product_bom()` - 检查产品是否有BOM
  - `get_product_bom()` - 获取产品BOM
  - `get_bom_items()` - 获取BOM明细
  - `get_inventory_stock()` - 获取库存数量

## 测试要点

1. **权限测试**：验证只能审核自己创建的订单
2. **状态测试**：验证只能审核未审核的订单
3. **库存测试**：验证库存检查和需求生成逻辑
4. **BOM测试**：验证BOM展开和物料需求计算
5. **日志测试**：验证操作日志记录

## 移除流程审批功能

### 已移除的内容

1. **审批信息标签页**：移除了订单详情页面的"审批信息"标签页
2. **反审按钮**：移除了订单状态旁边的"反审"按钮
3. **审批组件**：移除了`oaCheck`模块的引用和初始化
4. **审批流程加载**：移除了审批流程的AJAX加载代码
5. **提交审核按钮**：移除了提交审核的相关事件处理
6. **部门主管审批提示**：简化了发货条件的显示逻辑

### 修改的文件

- `app/customer/view/order/view.html` - 订单详情页面
  - 移除审批信息标签页和相关JavaScript代码
  - 简化操作按钮显示逻辑
  - 清理审批相关的CSS样式

### 简化后的逻辑

- **订单状态判断**：从`check_status == 2`改为`status > 0`
- **发货条件**：移除部门主管审批的条件判断
- **标签页结构**：保留付款记录、发货记录、关联单据、操作记录四个标签页

## 关联单据功能

### 新增功能

1. **关联单据标签页**：在订单详情页面新增"关联单据"标签页
2. **默认显示**：页面打开时默认显示"付款记录"标签页
3. **关联单据类型**：
   - 物料需求单
   - 采购订单
   - 库存调拨单
   - 发货单
   - 发票

### 实现细节

#### 控制器方法

**`getRelatedDocuments()` - 获取关联单据**

**位置**：`app/customer/controller/Order.php`

**功能**：
- 根据订单ID获取所有相关联的单据
- 支持多种单据类型的查询
- 按创建时间倒序排列
- 返回统一格式的数据结构

**查询逻辑**：
```php
// 1. 物料需求单：通过order_id关联
$materialRequirements = Db::name('material_requirement')
    ->where('order_id', $id)
    ->select();

// 2. 采购订单：通过source_order_id和source_type关联
$purchaseOrders = Db::name('purchase_order')
    ->where('source_order_id', $id)
    ->where('source_type', 'customer_order')
    ->select();

// 3. 库存调拨单：通过source_order_id和source_type关联
$inventoryTransfers = Db::name('oa_inventory_transfer')
    ->where('source_order_id', $id)
    ->where('source_type', 'customer_order')
    ->select();

// 4. 发货单：通过order_id关联
$deliveries = Db::name('customer_delivery')
    ->where('order_id', $id)
    ->select();

// 5. 发票：通过order_id关联
$invoices = Db::name('customer_invoice')
    ->where('order_id', $id)
    ->select();
```

#### 前端界面

**视图文件**：`app/customer/view/order/view_related.html`

**功能特点**：
- 表格形式展示关联单据
- 支持点击单据编号查看详情
- 不同单据类型显示不同的状态样式
- 无数据时显示友好提示
- AJAX异步加载数据

**表格字段**：
- 序号
- 单据类型
- 单据编号（可点击查看详情）
- 单据状态（带颜色标识）
- 产品/物料名称
- 创建人
- 创建时间
- 操作（查看按钮）

#### 状态管理

**状态样式映射**：
```javascript
var statusMap = {
    'purchase_order': {
        0: 'layui-bg-gray',    // 待审核
        1: 'layui-bg-blue',    // 已审核
        2: 'layui-bg-green',   // 已完成
        3: 'layui-bg-red'      // 已取消
    },
    'material_requirement': {
        0: 'layui-bg-gray',    // 未处理
        1: 'layui-bg-blue',    // 处理中
        2: 'layui-bg-green'    // 已完成
    },
    // ... 其他单据类型
};
```

#### 单据查看

**查看功能**：
- 支持跳转到对应单据的详情页面
- 使用`tool.side()`方法打开侧边栏
- 不同单据类型对应不同的URL路径

**URL映射**：
```javascript
var urlMap = {
    'purchase_order': '/purchase/order/view?id=' + id,
    'material_requirement': '/warehouse/MaterialRequirement/view?id=' + id,
    'inventory_transfer': '/warehouse/InventoryTransfer/view?id=' + id,
    'delivery': '/customer/delivery/view?id=' + id,
    'invoice': '/finance/invoice/view?id=' + id
};
```

### 标签页切换逻辑

**切换事件处理**：
```javascript
element.on('tab(purchase)', function(data){
    let index = data.index;
    if(index == 0){ // 付款记录选项卡
        loadPaymentLog();
    }else if(index === 1){ // 发货记录选项卡
        loadDeliveryLog();
    } else if(index === 2){ // 关联单据选项卡
        loadRelatedDocuments();
    } else if(index === 3){ // 操作记录选项卡
        log();
    }
});
```

**默认加载**：
- 页面打开时默认显示付款记录标签页
- 使用`$(document).ready()`确保页面加载完成后执行

### 相关文件

- **控制器**：`app/customer/controller/Order.php` - 添加`getRelatedDocuments()`方法
- **主视图**：`app/customer/view/order/view.html` - 添加关联单据标签页和切换逻辑
- **关联单据视图**：`app/customer/view/order/view_related.html` - 关联单据的展示界面

## 后续优化建议

1. **批量审核**：支持批量选择订单进行审核
2. **审核提醒**：库存不足时给出明确提醒
3. **需求预览**：审核前预览将要生成的物料需求
4. **权限扩展**：支持管理员审核他人订单
5. **状态管理**：完善订单状态的流转逻辑





已完成的功能
1. 控制器方法
在 app/customer/controller/Order.php 中添加了 simpleCheck() 方法
实现了完整的自审核逻辑，包括：
权限检查（只能审核自己创建的订单）
状态验证（只能审核未审核的订单）
订单状态更新
自动库存检查和BOM展开
物料需求生成
操作日志记录
2. 前端界面
在订单列表中添加了"审核"按钮
按钮只在 check_status == 0（未审核）时显示
添加了审核确认对话框，提醒用户审核后的影响
实现了AJAX调用和结果处理
3. 库存处理逻辑
有BOM的产品：自动展开BOM，检查每个物料的库存
普通产品：直接检查产品库存
库存不足时：自动生成物料需求记录到 material_requirement 表
允许超卖：库存不足不阻止审核，采用"后补货"策略
🔧 核心特性
自审核机制：用户只能审核自己创建的订单
一键操作：无需复杂的审核流程，点击即可完成
智能库存检查：自动检查库存并生成补货需求
BOM支持：完全兼容新的BOM系统
操作安全：有确认对话框和权限控制
📋 使用方法
创建订单：用户正常创建销售订单
自审核：在订单列表中点击"审核"按钮
确认审核：在弹出的确认框中点击确定
自动处理：系统自动检查库存、生成需求、更新状态
🎨 界面效果
未审核订单显示绿色"审核"按钮
点击后弹出确认对话框，提醒库存检查功能
审核成功后按钮消失，订单状态更新为"已通过"
📊 库存不足处理策略
当前系统采用**"允许超卖，后续补货"**的策略：

✅ 订单审核：即使库存不足也允许审核通过
📝 需求记录：将缺口数量记录到物料需求表
🔄 后续补货：通过采购或生产满足需求
🚚 发货控制：实际发货时再次检查库存
这种方式的优点是不会丢失订单，支持预售模式，缺点是可能出现延期交货。

## 9. 选项卡统一改造

### 9.1 功能概述
将销售订单详情页面的所有选项卡（付款记录、发货记录、关联单据、操作记录）统一改造为AJAX加载模式，提供一致的用户体验。

### 9.2 改造内容

#### 9.2.1 付款记录选项卡
- **改造前**: 使用 `{include file="order/view_payment" /}` 直接包含模板，使用 layui table 组件
- **改造后**: 使用 AJAX 加载数据，统一的表格样式
- **数据接口**: `/api/index/get_list?name=customer_order_payment&action_id={order_id}`
- **显示字段**: 序号、收款日期、支付类型、金额、到账状态、确认时间、操作时间、操作

#### 9.2.2 发货记录选项卡
- **改造前**: 使用 `{include file="order/view_delivery" /}` 直接包含模板，使用 layui table 组件
- **改造后**: 使用 AJAX 加载数据，统一的表格样式
- **数据接口**: `/api/index/get_list?name=customer_delivery&action_id={order_id}`
- **显示字段**: 序号、发货单号、预计发货时间、联系人、收货地址、状态、操作时间、操作

#### 9.2.3 关联单据选项卡
- **实现方式**: 使用 AJAX 动态加载关联单据数据
- **数据接口**: `/customer/order/getRelatedDocuments?id={order_id}`
- **显示字段**: 序号、单据类型、单据编号、单据状态、产品/物料名称、创建人、创建时间、操作
- **关联单据类型**: 物料需求单、采购订单、库存调拨单、发货单、发票

### 9.3 统一特性
1. **一致的加载体验**: 所有选项卡都使用相同的加载动画和错误处理
2. **统一的表格样式**: 使用 `layui-table` 和 `lay-skin="line"` 样式
3. **友好的空数据提示**: 无数据时显示统一的空状态图标和提示文字
4. **状态标识**: 使用 layui badge 组件显示不同状态，颜色统一
5. **操作按钮**: 统一的查看按钮样式和交互方式

### 9.4 默认行为
- **默认选项卡**: 页面加载时默认显示"付款记录"选项卡
- **自动加载**: 页面初始化时自动加载付款记录数据
- **按需加载**: 其他选项卡在用户点击时才加载数据

### 9.5 技术实现
- **JavaScript函数**: `loadPaymentLog()`, `loadDeliveryLog()`, `loadRelatedDocuments()`
- **查看详情函数**: `viewPaymentDetail()`, `viewDeliveryDetail()`, `viewDocument()`
- **工具函数**: `formatDateTime()`, `getStatusClass()`, `getStatusText()`
- **初始化**: 在 `gouguInit()` 函数中统一管理所有功能

## 总结

销售订单自审核功能为用户提供了一个简单、高效的订单审核方式，避免了复杂的工作流程，同时保证了库存检查和BOM验证的准确性。通过合理的权限控制和状态管理，确保了系统的安全性和数据一致性。

选项卡统一改造提升了用户体验的一致性，所有数据都采用AJAX按需加载，提高了页面加载速度和响应性能。

📁 相关文件
控制器： app/customer/controller/Order.php
视图： app/customer/view/order/view.html
设计文档：app/customer/controller/销售订单自审核功能设计.md
现在您可以访问 http://tc.xinqiyu.cn:8830/customer/order/view?id=100 测试统一改造后的选项卡功能！
