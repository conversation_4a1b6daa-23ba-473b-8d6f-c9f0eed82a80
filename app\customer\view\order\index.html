{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                
            </ul>
        </div> 
    </div>
    
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="order_no" placeholder="订单编号/品名/型号" class="layui-input" />
        </div>
        {gt name="$is_leader" value="0"}
		<div class="layui-input-inline user-name" style="width:100px;">
			<input type="text" name="username" placeholder="下属员工" class="layui-input picker-sub" readonly />
			<input type="text" name="uid" value="" style="display:none" />	
		</div>
		{/gt}
        <div class="layui-input-inline" style="width:150px;">
            <select name="customer_id" lay-search>
                <option value="">选择客户</option>
                {volist name=":get_customer_list()" id="vo"}
                <option value="{$vo.id}">{$vo.name}</option>
                {/volist}
            </select>
        </div>
        
        <div class="layui-input-inline" style="width:120px;">
            <select name="order_type">
                <option value="">订单类型</option>
                <option value="1">现货单</option>
                <option value="2">账期单</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="status">
                <option value="">订单状态</option>
                <option value="0">草稿</option>
                <option value="1">处理中</option>
                <option value="2">已完成</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    
    <table class="layui-hide" id="table_order" lay-filter="table_order"></table>
</div>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="add">
            <span>+ 添加订单</span>
            <i class="layui-icon layui-icon-down layui-font-12"></i>
        </button>
    </div>
</script>

<script type="text/html" id="barDemo">
    <div class="layui-btn-group">

        <a class="layui-btn layui-btn-xs" lay-event="view">详情</a>
        {{#  if(d.check_status == 0 || d.check_status == 4){ }}
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
        {{#  } }}
        {{#  if(d.check_status == 0){ }}
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="check">审核</a>
        {{#  } }}
        {{#  if(d.check_status == 2){ }}
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="reverse_check">反审核</a>
        {{#  } }}
        <a class="layui-btn layui-btn-xs" lay-event="pay">收款</a>
        {{#  if(d.check_status > 0){ }}
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="ship">发货</a>
        {{#  } }}
    </div>
</script>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool','tablePlus','oaPicker','uploadPlus','laydatePlus'];

function gouguInit() {
   // var table = layui.tablePlus, dropdown = layui.dropdown, element = layui.element, tool = layui.tool,table = layui.table, form = layui.form;
   var table = layui.tablePlus, tool = layui.tool ,form = layui.form,element = layui.element, oaPicker = layui.oaPicker,uploadPlus=layui.uploadPlus,laydatePlus=layui.laydatePlus;

    //tab切换
    element.on('tab(tab)', function(data){
        $('[name="tab"]').val(data.index);
        $("#barsearchform")[0].reset();
        layui.pageTable.reload({where:{tab:data.index},page:{curr:1}});
        return false;
    });
    
    layui.pageTable = table.render({
        elem: "#table_order"
        ,title: "订单列表"
        ,toolbar: "#toolbarDemo"
        ,url: "/customer/order/index"
        ,page: true
        ,limit: 20
        ,cellMinWidth: 80
        ,height: 'full-152'
        ,cols: [[
            {field: 'id', title: 'ID', width: 80, align: 'center'}
            ,{field: 'order_no', title: '订单编号', width: 180, align: 'center'}
            ,{field: 'customer_name', title: '客户名称', width: 150, templet: function(d){
                if (!d.customer) return '';
                return d.customer.name;
            }}
            ,{field: 'order_type', title: '订单类型', width: 100, align: 'center', templet: function(d){
                return d.order_type == 1 ? '现金单' : '账期单';
            }}
            ,{field: 'tax_rate', title: '税率%', width: 100, align: 'right'}
            ,{field: 'invoice_status_text', title: '开票状态', width: 150, align: 'center', templet: function(d){
                return '<span class="layui-badge ' + d.invoice_status_class + '">' + d.invoice_status_text + '</span>';
            }}
            ,{field: 'total_amount', title: '订单金额', width: 150, align: 'right',sort: true, totalRow: true}
            ,{field: 'paid_amount', title: '已收金额', width: 150, align: 'right'}

            ,{field: 'status', title: '订单状态', width: 100, align: 'center', templet: function(d){
                var status = {
                    0: '<span class="layui-badge layui-bg-gray">未处理</span>',
                    1: '<span class="layui-badge layui-bg-yellow">处理中</span>',
                    2: '<span class="layui-badge layui-bg-black">已完成</span>',
                    3: '<span class="layui-badge layui-bg-black">无库存</span>',
                    4: '<span class="layui-badge layui-bg-black">部分入库</span>',
                    5: '<span class="layui-badge layui-bg-black">已完成</span>'

                };
                return status[d.status] || '';
                
            }}
            ,{field: 'check_status', title: '审核状态', width: 100, align: 'center', templet: function(d){
                var status = {
                    0: '<span class="layui-badge layui-bg-gray">未审核</span>',
                    1: '<span class="layui-badge layui-bg-blue">待审核</span>',
                    2: '<span class="layui-badge layui-bg-green">已通过</span>',
                    3: '<span class="layui-badge layui-bg-green">已拒绝</span>'
                };
                return status[d.check_status] || '';
            }}
            ,{field: 'delivery_date', title: '交期日期', width: 160, align: 'center', templet: function(d){
                return d.delivery_date ? layui.util.toDateString(d.delivery_date*1000) : '';
            }}
            ,{field: 'create_user_name', title: '制单人', width: 160, align: 'center'}
            ,{field: 'create_time', title: '创建时间', width: 160, align: 'center', templet: function(d){
                return layui.util.toDateString(d.create_time*1000);
            }}
            ,{field: 'right', title: '操作', width: 300, fixed: 'right', align: 'center', toolbar: '#barDemo'}
        ]],
        totalRow: true 
    });
    
    //表头工具栏事件
    table.on('toolbar(table_order)', function(obj){
        if(obj.event === 'add'){
            openOrderDialog('添加订单', "/customer/order/add");
        }
    });
    
    //监听工具条
    table.on('tool(table_order)', function(obj){
        var data = obj.data;
        if(obj.event === 'view'){
            openOrderDialog('订单详情', "/customer/order/view?id="+data.id);
        }else if(obj.event === 'pay'){
            tool.side("/finance/invoice/add_b?id="+data.id);
        }
        else if(obj.event === 'edit'){
            if(data.status > 0){
                layer.msg('已审核的订单不能修改');
                return;
            }
            openOrderDialog('编辑订单', "/customer/order/edit?id="+data.id);
        } else if(obj.event === 'del'){
            if(data.status > 0){
                layer.msg('已审核的订单不能删除');
                return;
            }
            layer.confirm('确定要删除该订单吗?', {icon: 3, title:'提示'}, function(index){
                let callback = function(e){
                    layer.msg(e.msg);
                    if(e.code == 0){
                        obj.del();

                    }
                }
                
               tool.delete("/customer/order/delete", {id: data.id}, callback);
                //tool.delete('{:url("delete")}', {id: data.id}, callback);
                layer.close(index);
            });
        } else if(obj.event === 'ship'){
            // 跳转到发货页面
            tool.side("/customer/delivery/add?order_id="+data.id);
        } else if(obj.event === 'check'){
            // 简单审核
            layer.confirm('确定要审核该订单吗？<br><span style="color:red;">审核后将自动检查库存并生成物料需求</span>', {
                icon: 3,
                title:'审核确认',
                area: ['400px', '200px']
            }, function(index){
                let callback = function(e){
                    layer.msg(e.msg);
                    if(e.code == 0){
                        layui.pageTable.reload();
                    }
                }

                tool.post("/customer/order/simpleCheck", {id: data.id}, callback);
                layer.close(index);
            });
        } else if(obj.event === 'reverse_check'){
            // 反审核
            reverseAuditOrder(data.id);
        }
    });

    /**
     * 反审核订单
     */
    function reverseAuditOrder(id) {
        // 先检查是否可以反审核
        $.get('/customer/order/checkReverseAudit', {id: id}, function(res) {
            if (res.code == 0) {
                // 可以反审核，显示确认对话框
                showReverseAuditConfirm(id);
            } else {
                // 不能反审核，显示原因
                layer.alert(res.msg, {
                    icon: 2,
                    title: '无法反审核',
                    area: ['400px', '300px']
                });
            }
        });
    }

    /**
     * 显示反审核确认对话框
     */
    function showReverseAuditConfirm(id) {
        layer.confirm(
            '确定要反审核此订单吗？<br>' +
            '<span style="color:red;">注意：反审核后订单将回到草稿状态，相关的物料需求等数据将被清理。</span>',
            {
                icon: 3,
                title: '反审核确认',
                area: ['450px', '200px'],
                btn: ['确定反审核', '取消']
            },
            function(index) {
                // 执行反审核
                executeReverseAudit(id);
                layer.close(index);
            }
        );
    }

    /**
     * 执行反审核操作
     */
    function executeReverseAudit(id) {
        var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

        $.post('/customer/order/reverseAudit', {id: id}, function(res) {
            layer.close(loadingIndex);

            if (res.code == 0) {
                layer.msg('反审核成功', {icon: 1});
                // 刷新表格
                layui.pageTable.reload();
            } else {
                layer.alert(res.msg, {
                    icon: 2,
                    title: '反审核失败',
                    area: ['400px', '200px']
                });
            }
        }).fail(function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，请重试', {icon: 2});
        });
    }

    // 打开订单弹窗函数
    function openOrderDialog(title, url) {
        layer.open({
            type: 2,
            title: title,
            content: url,
            area: ['95%', '95%'],
            maxmin: true,
            shadeClose: false,
            success: function(layero, index) {
                // 获取iframe页面的window对象
                var iframeWin = window[layero.find('iframe')[0]['name']];
                
                // 将当前页面的刷新方法传递给iframe
                iframeWin.refreshParent = function() {
                    layui.pageTable.reload();
                    layer.close(index);
                };
            }
        });
    }
}
</script>
{/block} 